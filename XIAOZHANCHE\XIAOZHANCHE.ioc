#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.CPN=STM32F103C8T6
Mcu.Family=STM32F1
Mcu.IP0=NVIC
Mcu.IP1=RCC
Mcu.IP2=SYS
Mcu.IP3=TIM2
Mcu.IP4=TIM3
Mcu.IP5=TIM4
Mcu.IP6=USART1
Mcu.IPNb=7
Mcu.Name=STM32F103C(8-B)Tx
Mcu.Package=LQFP48
Mcu.Pin0=PC14-OSC32_IN
Mcu.Pin1=PC15-OSC32_OUT
Mcu.Pin10=PB10
Mcu.Pin11=PB11
Mcu.Pin12=PB12
Mcu.Pin13=PB13
Mcu.Pin14=PB14
Mcu.Pin15=PB15
Mcu.Pin16=PA8
Mcu.Pin17=PA9
Mcu.Pin18=PA10
Mcu.Pin19=PA11
Mcu.Pin2=PD0-OSC_IN
Mcu.Pin20=PA12
Mcu.Pin21=PA13
Mcu.Pin22=PA14
Mcu.Pin23=PA15
Mcu.Pin24=PB3
Mcu.Pin25=PB4
Mcu.Pin26=PB5
Mcu.Pin27=PB6
Mcu.Pin28=PB7
Mcu.Pin29=PB8
Mcu.Pin3=PD1-OSC_OUT
Mcu.Pin30=PB9
Mcu.Pin31=VP_SYS_VS_Systick
Mcu.Pin32=VP_TIM2_VS_ClockSourceINT
Mcu.Pin4=PA0-WKUP
Mcu.Pin5=PA1
Mcu.Pin6=PA3
Mcu.Pin7=PA4
Mcu.Pin8=PA6
Mcu.Pin9=PA7
Mcu.PinsNb=33
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F103C8Tx
MxCube.Version=6.12.0
MxDb.Version=DB.6.0.120
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:1\:0\:true\:false\:true\:false\:true\:false
NVIC.TIM2_IRQn=true\:2\:0\:true\:false\:true\:true\:true\:true
NVIC.USART1_IRQn=true\:2\:0\:true\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA0-WKUP.Signal=S_TIM2_CH1_ETR
PA1.Signal=S_TIM2_CH2
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA11.GPIOParameters=GPIO_Label
PA11.GPIO_Label=G7
PA11.Locked=true
PA11.Signal=GPIO_Input
PA12.GPIOParameters=GPIO_Label
PA12.GPIO_Label=G6
PA12.Locked=true
PA12.Signal=GPIO_Input
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA15.GPIOParameters=GPIO_Label
PA15.GPIO_Label=G5
PA15.Locked=true
PA15.Signal=GPIO_Input
PA3.GPIOParameters=GPIO_PuPd,GPIO_Label
PA3.GPIO_Label=Key_reset
PA3.GPIO_PuPd=GPIO_PULLUP
PA3.Locked=true
PA3.Signal=GPIO_Input
PA4.GPIOParameters=GPIO_Label
PA4.GPIO_Label=L_S
PA4.Locked=true
PA4.Signal=GPIO_Output
PA6.Signal=S_TIM3_CH1
PA7.Signal=S_TIM3_CH2
PA8.GPIOParameters=GPIO_Label
PA8.GPIO_Label=G4
PA8.Locked=true
PA8.Signal=GPIO_Input
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB10.GPIOParameters=GPIO_PuPd,GPIO_Label
PB10.GPIO_Label=Key_yaw
PB10.GPIO_PuPd=GPIO_PULLUP
PB10.Locked=true
PB10.Signal=GPIO_Input
PB11.GPIOParameters=GPIO_PuPd,GPIO_Label
PB11.GPIO_Label=Key_mode
PB11.GPIO_PuPd=GPIO_PULLUP
PB11.Locked=true
PB11.Signal=GPIO_Input
PB12.GPIOParameters=GPIO_Label
PB12.GPIO_Label=MOTOR_1
PB12.Locked=true
PB12.Signal=GPIO_Output
PB13.GPIOParameters=GPIO_Label
PB13.GPIO_Label=MOTOR_2
PB13.Locked=true
PB13.Signal=GPIO_Output
PB14.GPIOParameters=GPIO_Label
PB14.GPIO_Label=MOTOR_3
PB14.Locked=true
PB14.Signal=GPIO_Output
PB15.GPIOParameters=GPIO_Label
PB15.GPIO_Label=MOTOR_4
PB15.Locked=true
PB15.Signal=GPIO_Output
PB3.GPIOParameters=GPIO_Label
PB3.GPIO_Label=G3
PB3.Locked=true
PB3.Signal=GPIO_Input
PB4.GPIOParameters=GPIO_Label
PB4.GPIO_Label=G2
PB4.Locked=true
PB4.Signal=GPIO_Input
PB5.GPIOParameters=GPIO_Label
PB5.GPIO_Label=G1
PB5.Locked=true
PB5.Signal=GPIO_Input
PB6.Signal=S_TIM4_CH1
PB7.Signal=S_TIM4_CH2
PB8.GPIOParameters=GPIO_Speed,PinState,GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultOutputPP
PB8.GPIO_Label=OLED_SCL
PB8.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_OD
PB8.GPIO_PuPd=GPIO_NOPULL
PB8.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PB8.Locked=true
PB8.PinState=GPIO_PIN_SET
PB8.Signal=GPIO_Output
PB9.GPIOParameters=GPIO_Speed,PinState,GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultOutputPP
PB9.GPIO_Label=OLED_SDA
PB9.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_OD
PB9.GPIO_PuPd=GPIO_NOPULL
PB9.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PB9.Locked=true
PB9.PinState=GPIO_PIN_SET
PB9.Signal=GPIO_Output
PC14-OSC32_IN.Mode=LSE-External-Oscillator
PC14-OSC32_IN.Signal=RCC_OSC32_IN
PC15-OSC32_OUT.Mode=LSE-External-Oscillator
PC15-OSC32_OUT.Signal=RCC_OSC32_OUT
PD0-OSC_IN.Mode=HSE-External-Oscillator
PD0-OSC_IN.Signal=RCC_OSC_IN
PD1-OSC_OUT.Mode=HSE-External-Oscillator
PD1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F103C8Tx
ProjectManager.FirmwarePackage=STM32Cube FW_F1 V1.8.6
ProjectManager.FreePins=true
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=XIAOZHANCHE.ioc
ProjectManager.ProjectName=XIAOZHANCHE
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_TIM2_Init-TIM2-false-HAL-true,4-MX_TIM3_Init-TIM3-false-HAL-true,5-MX_TIM4_Init-TIM4-false-HAL-true,6-MX_USART1_UART_Init-USART1-false-HAL-true
RCC.ADCFreqValue=36000000
RCC.AHBFreq_Value=72000000
RCC.APB1CLKDivider=RCC_HCLK_DIV2
RCC.APB1Freq_Value=36000000
RCC.APB1TimFreq_Value=72000000
RCC.APB2Freq_Value=72000000
RCC.APB2TimFreq_Value=72000000
RCC.FCLKCortexFreq_Value=72000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=72000000
RCC.IPParameters=ADCFreqValue,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,APB2TimFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,MCOFreq_Value,PLLCLKFreq_Value,PLLMCOFreq_Value,PLLMUL,PLLSourceVirtual,SYSCLKFreq_VALUE,SYSCLKSource,TimSysFreq_Value,USBFreq_Value,VCOOutput2Freq_Value
RCC.MCOFreq_Value=72000000
RCC.PLLCLKFreq_Value=72000000
RCC.PLLMCOFreq_Value=36000000
RCC.PLLMUL=RCC_PLL_MUL9
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.SYSCLKFreq_VALUE=72000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.TimSysFreq_Value=72000000
RCC.USBFreq_Value=72000000
RCC.VCOOutput2Freq_Value=8000000
SH.S_TIM2_CH1_ETR.0=TIM2_CH1,PWM Generation1 CH1
SH.S_TIM2_CH1_ETR.ConfNb=1
SH.S_TIM2_CH2.0=TIM2_CH2,PWM Generation2 CH2
SH.S_TIM2_CH2.ConfNb=1
SH.S_TIM3_CH1.0=TIM3_CH1,Encoder_Interface
SH.S_TIM3_CH1.ConfNb=1
SH.S_TIM3_CH2.0=TIM3_CH2,Encoder_Interface
SH.S_TIM3_CH2.ConfNb=1
SH.S_TIM4_CH1.0=TIM4_CH1,Encoder_Interface
SH.S_TIM4_CH1.ConfNb=1
SH.S_TIM4_CH2.0=TIM4_CH2,Encoder_Interface
SH.S_TIM4_CH2.ConfNb=1
TIM2.Channel-PWM\ Generation1\ CH1=TIM_CHANNEL_1
TIM2.Channel-PWM\ Generation2\ CH2=TIM_CHANNEL_2
TIM2.IPParameters=Channel-PWM Generation1 CH1,Channel-PWM Generation2 CH2,Prescaler,Period
TIM2.Period=1999
TIM2.Prescaler=71
TIM3.EncoderMode=TIM_ENCODERMODE_TI12
TIM3.IPParameters=EncoderMode
TIM4.EncoderMode=TIM_ENCODERMODE_TI12
TIM4.IPParameters=EncoderMode
USART1.IPParameters=VirtualMode
USART1.VirtualMode=VM_ASYNC
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM2_VS_ClockSourceINT.Mode=Internal
VP_TIM2_VS_ClockSourceINT.Signal=TIM2_VS_ClockSourceINT
board=custom
